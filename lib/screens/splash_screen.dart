import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:unstack/theme/app_theme.dart';
import 'package:unstack/screens/onboarding_screen.dart';
import 'package:unstack/views/home.dart';

class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen>
    with TickerProviderStateMixin {
  late AnimationController _logoController;
  late AnimationController _breathingController;
  var _breathe = 0.0;
  late AnimationController _textController;

  bool _showLogo = true;
  bool _showBreathingText = false;
  String _breathingText = "Breathe in";
  bool _isBreathingIn = true;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _startAnimationSequence();
  }

  void _initializeAnimations() {
    // Logo fade animation (1 second show + fade)
    _logoController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1500),
    );

    // Text fade animation
    _textController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );
  }

  void _startAnimationSequence() async {
    // Step 1: Show logo for 1 second
    await Future.delayed(const Duration(seconds: 1));

    // Step 2: Fade logo away
    if (mounted) {
      _logoController.forward();
      await Future.delayed(const Duration(milliseconds: 500));
      setState(() {
        _showLogo = false;
        _showBreathingText = true;
      });
      _textController.forward();
    }

    _breathingController =
        AnimationController(vsync: this, duration: Duration(seconds: 3));
    _breathingController.addStatusListener((status) {
      if (status == AnimationStatus.completed) {
        Future.delayed(Duration(seconds: 2), () {
          _breathingController.reverse();
        });
      } else if (status == AnimationStatus.dismissed) {
        Future.delayed(Duration(seconds: 2), () {
          _breathingController.forward();
        });
      }
    });

    _breathingController.addListener(() {
      setState(() {
        _breathe = _breathingController.value;
      });
    });
    _breathingController.forward();

    // Step 5: Change to "Breathe out" and reverse animation (3 seconds)
    // if (mounted) {
    //   _textController.reverse();
    //   await Future.delayed(const Duration(milliseconds: 150));
    //   setState(() {
    //     _breathingText = "Breathe out";
    //     _isBreathingIn = false;
    //   });
    //   _textController.forward();

    //   await Future.delayed(const Duration(milliseconds: 3000));
    // }

    // // Step 6: Navigate to next screen
    // if (mounted) {
    //   _initializeApp();
    // }
  }

  Future<void> _initializeApp() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final onboardingCompleted =
          prefs.getBool('onboarding_completed') ?? false;

      if (mounted) {
        if (onboardingCompleted) {
          _navigateToHome();
        } else {
          _navigateToOnboarding();
        }
      }
    } catch (e) {
      // If there's an error, default to onboarding
      if (mounted) {
        _navigateToOnboarding();
      }
    }
  }

  void _navigateToOnboarding() {
    Navigator.of(context).pushReplacement(
      PageRouteBuilder(
        pageBuilder: (context, animation, secondaryAnimation) =>
            const OnboardingScreen(),
        transitionsBuilder: (context, animation, secondaryAnimation, child) {
          return FadeTransition(
            opacity: animation.drive(
              CurveTween(curve: Curves.easeInOut),
            ),
            child: child,
          );
        },
        transitionDuration: const Duration(milliseconds: 600),
      ),
    );
  }

  void _navigateToHome() {
    Navigator.of(context).pushReplacement(
      PageRouteBuilder(
        pageBuilder: (context, animation, secondaryAnimation) =>
            const HomePage(),
        transitionsBuilder: (context, animation, secondaryAnimation, child) {
          return FadeTransition(
            opacity: animation.drive(
              CurveTween(curve: Curves.easeInOut),
            ),
            child: child,
          );
        },
        transitionDuration: const Duration(milliseconds: 600),
      ),
    );
  }

  @override
  void dispose() {
    _logoController.dispose();
    _breathingController.dispose();
    _textController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final size = 700.0 - 100.0 * _breathe;

    return Scaffold(
      backgroundColor: AppColors.backgroundPrimary,
      body: SizedBox(
        height: double.infinity,
        width: double.infinity,
        child: Stack(
          alignment: Alignment.center,
          children: [
            // Logo (shows for 1 second then fades)
            if (_showLogo)
              FadeTransition(
                opacity: Tween<double>(begin: 1.0, end: 0.0).animate(
                  CurvedAnimation(
                    parent: _logoController,
                    curve: Curves.easeOut,
                  ),
                ),
                child: Image.asset(
                  'assets/logo/unstack.png',
                  height: 500,
                  width: 500,
                ),
              ),

            if (_showBreathingText)
              Positioned(
                top: 80,
                child: FadeTransition(
                  opacity: _textController,
                  child: Text(
                    _breathingText,
                    style: AppTextStyles.h1.copyWith(
                      fontSize: 36,
                      fontWeight: FontWeight.w600,
                      color: AppColors.textPrimary,
                      letterSpacing: 1.2,
                    ),
                  ),
                ),
              ),
            if (_showBreathingText)
              Positioned(
                bottom: -300,
                child: Container(
                  height: size * 1.2,
                  width: size * 1.2,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: AppColors.accentPurple,
                  ),
                ),
              )
          ],
        ),
      ),
    );
  }
}
