import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:unstack/theme/app_theme.dart';
import 'package:unstack/screens/onboarding_screen.dart';
import 'package:unstack/views/home.dart';

class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen>
    with TickerProviderStateMixin {
  late AnimationController _logoController;
  late AnimationController _breathingController;
  late AnimationController _textController;

  bool _showLogo = true;
  bool _showBreathingText = false;
  String _breathingText = "Breathe in";
  bool _isBreathingIn = true;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _startAnimationSequence();
  }

  void _initializeAnimations() {
    // Logo fade animation (1 second show + fade)
    _logoController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1500),
    );

    // Complete breathing cycle animation (7 seconds total: 3s in + 1s hold + 3s out)
    _breathingController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 7000),
    );

    // Text fade animation
    _textController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );

    // Listen to breathing animation progress to update text and breathing state
    _breathingController.addListener(() {
      final progress = _breathingController.value;

      // Determine current phase and update text accordingly
      if (progress <= 0.428) {
        // 0 to 3 seconds (breathe in phase)
        if (_breathingText != "Breathe in") {
          setState(() {
            _breathingText = "Breathe in";
            _isBreathingIn = true;
          });
        }
      } else if (progress <= 0.571) {
        // 3 to 4 seconds (hold phase)
        // Keep current state during hold
      } else {
        // 4 to 7 seconds (breathe out phase)
        if (_breathingText != "Breathe out") {
          setState(() {
            _breathingText = "Breathe out";
            _isBreathingIn = false;
          });
        }
      }
    });

    // When breathing animation completes, navigate to next screen
    _breathingController.addStatusListener((status) {
      if (status == AnimationStatus.completed) {
        _initializeApp();
      }
    });
  }

  void _startAnimationSequence() async {
    // Step 1: Show logo for 1 second
    await Future.delayed(const Duration(seconds: 1));

    // Step 2: Fade logo away
    if (mounted) {
      _logoController.forward();
      await Future.delayed(const Duration(milliseconds: 500));
      setState(() {
        _showLogo = false;
        _showBreathingText = true;
      });
      _textController.forward();
    }

    // Step 3: Start the complete breathing cycle
    if (mounted) {
      _breathingController.forward();
    }
  }

  Future<void> _initializeApp() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final onboardingCompleted =
          prefs.getBool('onboarding_completed') ?? false;

      if (mounted) {
        if (onboardingCompleted) {
          _navigateToHome();
        } else {
          _navigateToOnboarding();
        }
      }
    } catch (e) {
      // If there's an error, default to onboarding
      if (mounted) {
        _navigateToOnboarding();
      }
    }
  }

  void _navigateToOnboarding() {
    Navigator.of(context).pushReplacement(
      PageRouteBuilder(
        pageBuilder: (context, animation, secondaryAnimation) =>
            const OnboardingScreen(),
        transitionsBuilder: (context, animation, secondaryAnimation, child) {
          return FadeTransition(
            opacity: animation.drive(
              CurveTween(curve: Curves.easeInOut),
            ),
            child: child,
          );
        },
        transitionDuration: const Duration(milliseconds: 600),
      ),
    );
  }

  void _navigateToHome() {
    Navigator.of(context).pushReplacement(
      PageRouteBuilder(
        pageBuilder: (context, animation, secondaryAnimation) =>
            const HomePage(),
        transitionsBuilder: (context, animation, secondaryAnimation, child) {
          return FadeTransition(
            opacity: animation.drive(
              CurveTween(curve: Curves.easeInOut),
            ),
            child: child,
          );
        },
        transitionDuration: const Duration(milliseconds: 600),
      ),
    );
  }

  @override
  void dispose() {
    _logoController.dispose();
    _breathingController.dispose();
    _textController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.backgroundPrimary,
      body: SizedBox(
        height: double.infinity,
        width: double.infinity,
        child: Stack(
          alignment: Alignment.center,
          children: [
            // Logo (shows for 1 second then fades)
            if (_showLogo)
              FadeTransition(
                opacity: Tween<double>(begin: 1.0, end: 0.0).animate(
                  CurvedAnimation(
                    parent: _logoController,
                    curve: Curves.easeOut,
                  ),
                ),
                child: Image.asset(
                  'assets/logo/unstack.png',
                  height: 200,
                  width: 200,
                ),
              ),

            // Breathing text at the top
            if (_showBreathingText)
              Positioned(
                top: MediaQuery.of(context).size.height * 0.25,
                child: FadeTransition(
                  opacity: _textController,
                  child: Text(
                    _breathingText,
                    style: AppTextStyles.h1.copyWith(
                      fontSize: 28,
                      fontWeight: FontWeight.w400,
                      color: AppColors.textPrimary,
                      letterSpacing: 1.2,
                    ),
                  ),
                ),
              ),

            // Breathing circle with face
            if (_showBreathingText)
              AnimatedBuilder(
                animation: _breathingController,
                builder: (context, child) {
                  final progress = _breathingController.value;
                  double circleProgress;

                  // Calculate circle size based on breathing phase
                  if (progress <= 0.428) {
                    // Breathe in phase (0 to 3 seconds): expand from 0 to 1
                    circleProgress =
                        Curves.easeInOut.transform(progress / 0.428);
                  } else if (progress <= 0.571) {
                    // Hold phase (3 to 4 seconds): stay at maximum
                    circleProgress = 1.0;
                  } else {
                    // Breathe out phase (4 to 7 seconds): contract from 1 to 0
                    final outProgress = (progress - 0.571) / 0.429;
                    circleProgress =
                        Curves.easeInOut.transform(1.0 - outProgress);
                  }

                  final size = 150.0 + (100.0 * circleProgress);

                  return Container(
                    height: size,
                    width: size,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: AppColors.accentGreen,
                      boxShadow: [
                        BoxShadow(
                          color: AppColors.accentGreen.withValues(alpha: 0.3),
                          blurRadius: 20,
                          spreadRadius: 5,
                        ),
                      ],
                    ),
                    child: Stack(
                      alignment: Alignment.center,
                      children: [
                        // Peaceful face
                        Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            // Closed eyes
                            Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Container(
                                  width: size * 0.08,
                                  height: 3,
                                  decoration: BoxDecoration(
                                    color: AppColors.backgroundPrimary,
                                    borderRadius: BorderRadius.circular(2),
                                  ),
                                ),
                                SizedBox(width: size * 0.15),
                                Container(
                                  width: size * 0.08,
                                  height: 3,
                                  decoration: BoxDecoration(
                                    color: AppColors.backgroundPrimary,
                                    borderRadius: BorderRadius.circular(2),
                                  ),
                                ),
                              ],
                            ),
                            SizedBox(height: size * 0.08),
                            // Peaceful smile
                            Container(
                              width: size * 0.25,
                              height: size * 0.12,
                              decoration: BoxDecoration(
                                border: Border(
                                  bottom: BorderSide(
                                    color: AppColors.backgroundPrimary,
                                    width: 3,
                                  ),
                                ),
                                borderRadius: BorderRadius.only(
                                  bottomLeft: Radius.circular(size * 0.12),
                                  bottomRight: Radius.circular(size * 0.12),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  );
                },
              ),
          ],
        ),
      ),
    );
  }
}
