import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:smooth_page_indicator/smooth_page_indicator.dart';
import 'package:unstack/theme/app_theme.dart';
import 'package:unstack/widgets/glass_container.dart';
import 'package:unstack/screens/name_input_screen.dart';

class OnboardingScreen extends StatefulWidget {
  const OnboardingScreen({super.key});

  @override
  State<OnboardingScreen> createState() => _OnboardingScreenState();
}

class _OnboardingScreenState extends State<OnboardingScreen> {
  final PageController _pageController = PageController();
  int _currentPage = 0;

  final List<OnboardingData> _onboardingData = [
    OnboardingData(
      title: "Master Your Tasks",
      subtitle: "Organize, prioritize, and conquer your to-do list with elegant simplicity",
      icon: Icons.task_alt_rounded,
      color: AppColors.accentPurple,
      description: "Transform chaos into clarity with our intuitive task management system designed for modern productivity.",
    ),
    OnboardingData(
      title: "Focus with Pomodoro",
      subtitle: "Boost productivity with scientifically-proven time management techniques",
      icon: Icons.timer_rounded,
      color: AppColors.accentBlue,
      description: "Stay focused and avoid burnout with customizable Pomodoro sessions that adapt to your workflow.",
    ),
    OnboardingData(
      title: "Track Your Progress",
      subtitle: "Visualize achievements and build momentum towards your goals",
      icon: Icons.trending_up_rounded,
      color: AppColors.accentGreen,
      description: "Gain insights into your productivity patterns and celebrate every milestone on your journey to success.",
    ),
  ];

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  void _nextPage() {
    if (_currentPage < _onboardingData.length - 1) {
      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    } else {
      _navigateToNameInput();
    }
  }

  void _navigateToNameInput() {
    Navigator.of(context).pushReplacement(
      PageRouteBuilder(
        pageBuilder: (context, animation, secondaryAnimation) => const NameInputScreen(),
        transitionsBuilder: (context, animation, secondaryAnimation, child) {
          return SlideTransition(
            position: animation.drive(
              Tween(begin: const Offset(1.0, 0.0), end: Offset.zero).chain(
                CurveTween(curve: Curves.easeInOut),
              ),
            ),
            child: child,
          );
        },
        transitionDuration: const Duration(milliseconds: 400),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.backgroundPrimary,
      body: SafeArea(
        child: Column(
          children: [
            // Skip button
            Padding(
              padding: const EdgeInsets.all(AppSpacing.lg),
              child: Align(
                alignment: Alignment.topRight,
                child: TextButton(
                  onPressed: _navigateToNameInput,
                  child: Text(
                    'Skip',
                    style: AppTextStyles.bodyMedium.copyWith(
                      color: AppColors.textSecondary,
                    ),
                  ),
                ),
              ),
            ),
            
            // Page view
            Expanded(
              child: PageView.builder(
                controller: _pageController,
                onPageChanged: (index) {
                  setState(() {
                    _currentPage = index;
                  });
                },
                itemCount: _onboardingData.length,
                itemBuilder: (context, index) {
                  return OnboardingPage(
                    data: _onboardingData[index],
                    isActive: index == _currentPage,
                  );
                },
              ),
            ),
            
            // Page indicator and button
            Padding(
              padding: const EdgeInsets.all(AppSpacing.xl),
              child: Column(
                children: [
                  // Page indicator
                  SmoothPageIndicator(
                    controller: _pageController,
                    count: _onboardingData.length,
                    effect: ExpandingDotsEffect(
                      activeDotColor: AppColors.accentPurple,
                      dotColor: AppColors.textMuted,
                      dotHeight: 8,
                      dotWidth: 8,
                      expansionFactor: 3,
                      spacing: 8,
                    ),
                  ).animate().fadeIn(delay: 200.ms),
                  
                  const SizedBox(height: AppSpacing.xl),
                  
                  // Get Started button
                  GlassButton(
                    text: _currentPage == _onboardingData.length - 1 
                        ? 'Get Started' 
                        : 'Next',
                    onPressed: _nextPage,
                    width: double.infinity,
                    height: 56,
                  ).animate().slideY(
                    begin: 0.3,
                    duration: 400.ms,
                    curve: Curves.easeOut,
                  ).fadeIn(),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class OnboardingData {
  final String title;
  final String subtitle;
  final String description;
  final IconData icon;
  final Color color;

  OnboardingData({
    required this.title,
    required this.subtitle,
    required this.description,
    required this.icon,
    required this.color,
  });
}

class OnboardingPage extends StatelessWidget {
  final OnboardingData data;
  final bool isActive;

  const OnboardingPage({
    super.key,
    required this.data,
    required this.isActive,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: AppSpacing.xl),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Icon container
          GlassContainer(
            width: 120,
            height: 120,
            borderRadius: AppBorderRadius.full,
            opacity: 0.1,
            borderColor: data.color,
            child: Icon(
              data.icon,
              size: 60,
              color: data.color,
            ),
          ).animate(target: isActive ? 1 : 0)
            .scale(
              begin: const Offset(0.8, 0.8),
              duration: 600.ms,
              curve: Curves.elasticOut,
            )
            .fadeIn(duration: 400.ms),
          
          const SizedBox(height: AppSpacing.xl),
          
          // Title
          Text(
            data.title,
            style: AppTextStyles.h1,
            textAlign: TextAlign.center,
          ).animate(target: isActive ? 1 : 0)
            .slideY(
              begin: 0.3,
              duration: 500.ms,
              curve: Curves.easeOut,
            )
            .fadeIn(delay: 200.ms),
          
          const SizedBox(height: AppSpacing.md),
          
          // Subtitle
          Text(
            data.subtitle,
            style: AppTextStyles.bodyLarge.copyWith(
              color: AppColors.textSecondary,
            ),
            textAlign: TextAlign.center,
          ).animate(target: isActive ? 1 : 0)
            .slideY(
              begin: 0.3,
              duration: 500.ms,
              curve: Curves.easeOut,
            )
            .fadeIn(delay: 300.ms),
          
          const SizedBox(height: AppSpacing.lg),
          
          // Description
          Text(
            data.description,
            style: AppTextStyles.bodyMedium.copyWith(
              color: AppColors.textMuted,
              height: 1.6,
            ),
            textAlign: TextAlign.center,
          ).animate(target: isActive ? 1 : 0)
            .slideY(
              begin: 0.3,
              duration: 500.ms,
              curve: Curves.easeOut,
            )
            .fadeIn(delay: 400.ms),
        ],
      ),
    );
  }
}
