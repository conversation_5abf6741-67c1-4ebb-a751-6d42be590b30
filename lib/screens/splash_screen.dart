import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:unstack/theme/app_theme.dart';
import 'package:unstack/screens/onboarding_screen.dart';
import 'package:unstack/views/home.dart';

class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen>
    with TickerProviderStateMixin {
  late AnimationController _logoController;
  late AnimationController _breathingController;
  late AnimationController _textController;

  bool _showLogo = true;
  bool _showBreathingText = false;
  String _breathingText = "Breathe in";
  bool _isBreathingIn = true;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _startAnimationSequence();
  }

  void _initializeAnimations() {
    // Logo fade animation (1 second show + fade)
    _logoController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1500),
    );

    // Breathing circle animation (2 seconds in, 1 second hold, 2 seconds out)
    _breathingController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 2000),
    );

    // Text fade animation
    _textController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );
  }

  void _startAnimationSequence() async {
    // Step 1: Show logo for 1 second
    await Future.delayed(const Duration(seconds: 1));

    // Step 2: Fade logo away
    if (mounted) {
      _logoController.forward();
      await Future.delayed(const Duration(milliseconds: 500));
      setState(() {
        _showLogo = false;
        _showBreathingText = true;
      });
      _textController.forward();
    }

    // Step 3: Breathe in animation (2 seconds)
    if (mounted) {
      _breathingController.forward();
      await Future.delayed(const Duration(milliseconds: 2000));
    }

    // Step 4: Hold for 1 second
    if (mounted) {
      await Future.delayed(const Duration(seconds: 1));
    }

    // Step 5: Change to "Breathe out" and reverse animation (2 seconds)
    if (mounted) {
      _textController.reverse();
      await Future.delayed(const Duration(milliseconds: 150));
      setState(() {
        _breathingText = "Breathe out";
        _isBreathingIn = false;
      });
      _textController.forward();
      _breathingController.reverse();
      await Future.delayed(const Duration(milliseconds: 2000));
    }

    // Step 6: Navigate to next screen
    if (mounted) {
      _initializeApp();
    }
  }

  Future<void> _initializeApp() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final onboardingCompleted =
          prefs.getBool('onboarding_completed') ?? false;

      if (mounted) {
        if (onboardingCompleted) {
          _navigateToHome();
        } else {
          _navigateToOnboarding();
        }
      }
    } catch (e) {
      // If there's an error, default to onboarding
      if (mounted) {
        _navigateToOnboarding();
      }
    }
  }

  void _navigateToOnboarding() {
    Navigator.of(context).pushReplacement(
      PageRouteBuilder(
        pageBuilder: (context, animation, secondaryAnimation) =>
            const OnboardingScreen(),
        transitionsBuilder: (context, animation, secondaryAnimation, child) {
          return FadeTransition(
            opacity: animation.drive(
              CurveTween(curve: Curves.easeInOut),
            ),
            child: child,
          );
        },
        transitionDuration: const Duration(milliseconds: 600),
      ),
    );
  }

  void _navigateToHome() {
    Navigator.of(context).pushReplacement(
      PageRouteBuilder(
        pageBuilder: (context, animation, secondaryAnimation) =>
            const HomePage(),
        transitionsBuilder: (context, animation, secondaryAnimation, child) {
          return FadeTransition(
            opacity: animation.drive(
              CurveTween(curve: Curves.easeInOut),
            ),
            child: child,
          );
        },
        transitionDuration: const Duration(milliseconds: 600),
      ),
    );
  }

  @override
  void dispose() {
    _logoController.dispose();
    _breathingController.dispose();
    _textController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.backgroundPrimary,
      body: SizedBox(
        height: double.infinity,
        width: double.infinity,
        child: Stack(
          alignment: Alignment.center,
          children: [
            // Logo (shows for 1 second then fades)
            if (_showLogo)
              FadeTransition(
                opacity: Tween<double>(begin: 1.0, end: 0.0).animate(
                  CurvedAnimation(
                    parent: _logoController,
                    curve: Curves.easeOut,
                  ),
                ),
                child: Image.asset(
                  'assets/logo/unstack.png',
                  height: 200,
                  width: 200,
                ),
              ),

            // Breathing text at the top
            if (_showBreathingText)
              Positioned(
                top: MediaQuery.of(context).size.height * 0.2,
                child: FadeTransition(
                  opacity: _textController,
                  child: Text(
                    _breathingText,
                    style: AppTextStyles.h1.copyWith(
                      fontSize: 32,
                      fontWeight: FontWeight.w300,
                      color: AppColors.textPrimary,
                    ),
                  ),
                ),
              ),

            // Breathing circle
            if (_showBreathingText)
              AnimatedBuilder(
                animation: _breathingController,
                builder: (context, child) {
                  final progress = _isBreathingIn
                      ? _breathingController.value
                      : 1.0 - _breathingController.value;
                  final size = 100.0 + (200.0 * progress);

                  return Container(
                    height: size,
                    width: size,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: AppColors.accentPurple.withValues(alpha: 0.3),
                      border: Border.all(
                        color: AppColors.accentPurple.withValues(alpha: 0.6),
                        width: 2,
                      ),
                    ),
                  );
                },
              ),
          ],
        ),
      ),
    );
  }
}
