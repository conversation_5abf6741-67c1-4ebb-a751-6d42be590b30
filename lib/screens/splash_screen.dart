import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:unstack/theme/app_theme.dart';
import 'package:unstack/widgets/glass_container.dart';
import 'package:unstack/screens/onboarding_screen.dart';
import 'package:unstack/views/home.dart';

class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen>
    with TickerProviderStateMixin {
  late AnimationController _breathingController;
  var _breathe = 0.0;

  @override
  void initState() {
    super.initState();
    _initializeApp();
    _breathingController = AnimationController(
        vsync: this, duration: Duration(milliseconds: 5000));
    _breathingController.addStatusListener((status) {
      if (status == AnimationStatus.completed) {
        _breathingController.reverse();
      } else if (status == AnimationStatus.dismissed) {
        _breathingController.forward();
      }
    });

    _breathingController.addListener(() {
      setState(() {
        _breathe = -_breathingController.value;
      });
    });
    _breathingController.forward();
  }

  Future<void> _initializeApp() async {
    // // Show splash screen for at least 2 seconds for better UX
    // await Future.delayed(const Duration(seconds: 2));

    // try {
    //   final prefs = await SharedPreferences.getInstance();
    //   final onboardingCompleted = prefs.getBool('onboarding_completed') ?? false;

    //   if (mounted) {
    //     if (onboardingCompleted) {
    //       _navigateToHome();
    //     } else {
    //       _navigateToOnboarding();
    //     }
    //   }
    // } catch (e) {
    //   // If there's an error, default to onboarding
    //   if (mounted) {
    //     _navigateToOnboarding();
    //   }
    // }
  }

  void _navigateToOnboarding() {
    Navigator.of(context).pushReplacement(
      PageRouteBuilder(
        pageBuilder: (context, animation, secondaryAnimation) =>
            const OnboardingScreen(),
        transitionsBuilder: (context, animation, secondaryAnimation, child) {
          return FadeTransition(
            opacity: animation.drive(
              CurveTween(curve: Curves.easeInOut),
            ),
            child: child,
          );
        },
        transitionDuration: const Duration(milliseconds: 600),
      ),
    );
  }

  void _navigateToHome() {
    Navigator.of(context).pushReplacement(
      PageRouteBuilder(
        pageBuilder: (context, animation, secondaryAnimation) =>
            const HomePage(),
        transitionsBuilder: (context, animation, secondaryAnimation, child) {
          return FadeTransition(
            opacity: animation.drive(
              CurveTween(curve: Curves.easeInOut),
            ),
            child: child,
          );
        },
        transitionDuration: const Duration(milliseconds: 600),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final size = 700.0 - 300.0 * _breathe;
    return Scaffold(
      body: Container(
        height: double.infinity,
        child: Stack(
          alignment: Alignment.center,
          children: [
            // App logo/icon
            Image.asset(
              'assets/logo/unstack.png',
              height: 500,
              width: 500,
            ),
            Positioned(
              bottom: -200,
              child: Container(
                height: size * 1.2,
                width: size * 1.2,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: AppColors.accentPurple,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
