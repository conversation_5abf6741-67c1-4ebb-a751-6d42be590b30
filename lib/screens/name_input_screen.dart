import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:unstack/theme/app_theme.dart';
import 'package:unstack/widgets/glass_container.dart';
import 'package:unstack/views/home.dart';

class NameInputScreen extends StatefulWidget {
  const NameInputScreen({super.key});

  @override
  State<NameInputScreen> createState() => _NameInputScreenState();
}

class _NameInputScreenState extends State<NameInputScreen> {
  final TextEditingController _nameController = TextEditingController();
  final FocusNode _nameFocusNode = FocusNode();
  bool _isLoading = false;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    // Auto-focus the text field after a short delay
    Future.delayed(const Duration(milliseconds: 800), () {
      if (mounted) {
        _nameFocusNode.requestFocus();
      }
    });
  }

  @override
  void dispose() {
    _nameController.dispose();
    _nameFocusNode.dispose();
    super.dispose();
  }

  String? _validateName(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'Please enter your name';
    }
    if (value.trim().length < 2) {
      return 'Name must be at least 2 characters';
    }
    if (value.trim().length > 30) {
      return 'Name must be less than 30 characters';
    }
    return null;
  }

  Future<void> _saveName() async {
    final name = _nameController.text.trim();
    final validation = _validateName(name);

    if (validation != null) {
      setState(() {
        _errorMessage = validation;
      });
      return;
    }

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('user_name', name);
      await prefs.setBool('onboarding_completed', true);

      // Add a small delay for better UX
      await Future.delayed(const Duration(milliseconds: 500));

      if (mounted) {
        _navigateToHome();
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Something went wrong. Please try again.';
        _isLoading = false;
      });
    }
  }

  void _navigateToHome() {
    Navigator.of(context).pushReplacement(
      PageRouteBuilder(
        pageBuilder: (context, animation, secondaryAnimation) =>
            const HomePage(),
        transitionsBuilder: (context, animation, secondaryAnimation, child) {
          return FadeTransition(
            opacity: animation.drive(
              CurveTween(curve: Curves.easeInOut),
            ),
            child: child,
          );
        },
        transitionDuration: const Duration(milliseconds: 600),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.backgroundPrimary,
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(AppSpacing.xl),
          child: Column(
            children: [
              const Spacer(),

              // Welcome content
              Column(
                children: [
                  // Welcome icon
                  GlassContainer(
                    width: 100,
                    height: 100,
                    borderRadius: AppBorderRadius.full,
                    opacity: 0.1,
                    borderColor: AppColors.accentPurple,
                    child: const Icon(
                      Icons.person_add_rounded,
                      size: 50,
                      color: AppColors.accentPurple,
                    ),
                  )
                      .animate()
                      .scale(
                        begin: const Offset(0.8, 0.8),
                        duration: 600.ms,
                        curve: Curves.elasticOut,
                      )
                      .fadeIn(duration: 400.ms),

                  const SizedBox(height: AppSpacing.xl),

                  // Welcome title
                  Text(
                    'Welcome to Unstack!',
                    style: AppTextStyles.h1,
                    textAlign: TextAlign.center,
                  )
                      .animate()
                      .slideY(
                        begin: 0.3,
                        duration: 500.ms,
                        curve: Curves.easeOut,
                      )
                      .fadeIn(delay: 200.ms),

                  const SizedBox(height: AppSpacing.md),

                  // Subtitle
                  Text(
                    'Let\'s personalize your experience',
                    style: AppTextStyles.bodyLarge.copyWith(
                      color: AppColors.textSecondary,
                    ),
                    textAlign: TextAlign.center,
                  )
                      .animate()
                      .slideY(
                        begin: 0.3,
                        duration: 500.ms,
                        curve: Curves.easeOut,
                      )
                      .fadeIn(delay: 300.ms),

                  const SizedBox(height: AppSpacing.lg),

                  // Description
                  Text(
                    'We\'d love to know what to call you. Your name helps us create a more personal and engaging experience.',
                    style: AppTextStyles.bodyMedium.copyWith(
                      color: AppColors.textMuted,
                      height: 1.6,
                    ),
                    textAlign: TextAlign.center,
                  )
                      .animate()
                      .slideY(
                        begin: 0.3,
                        duration: 500.ms,
                        curve: Curves.easeOut,
                      )
                      .fadeIn(delay: 400.ms),
                ],
              ),

              const SizedBox(height: AppSpacing.xxxl),

              // Name input section
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'What should we call you?',
                    style: AppTextStyles.bodyLarge.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  )
                      .animate()
                      .slideX(
                        begin: -0.3,
                        duration: 500.ms,
                        curve: Curves.easeOut,
                      )
                      .fadeIn(delay: 600.ms),

                  const SizedBox(height: AppSpacing.md),

                  // Name input field
                  GlassContainer(
                    borderRadius: AppBorderRadius.md,
                    opacity: 0.05,
                    padding: const EdgeInsets.all(2),
                    child: TextField(
                      controller: _nameController,
                      focusNode: _nameFocusNode,
                      style: AppTextStyles.bodyLarge,
                      decoration: InputDecoration(
                        hintText: 'Enter your name',
                        border: InputBorder.none,
                        enabledBorder: InputBorder.none,
                        focusedBorder: InputBorder.none,
                        contentPadding: const EdgeInsets.symmetric(
                          horizontal: AppSpacing.lg,
                          vertical: AppSpacing.md,
                        ),
                        hintStyle: AppTextStyles.bodyLarge.copyWith(
                          color: AppColors.textMuted,
                        ),
                      ),
                      textInputAction: TextInputAction.done,
                      onSubmitted: (_) => _saveName(),
                      onChanged: (value) {
                        if (_errorMessage != null) {
                          setState(() {
                            _errorMessage = null;
                          });
                        }
                      },
                    ),
                  )
                      .animate()
                      .slideY(
                        begin: 0.3,
                        duration: 500.ms,
                        curve: Curves.easeOut,
                      )
                      .fadeIn(delay: 700.ms),

                  // Error message
                  if (_errorMessage != null)
                    Padding(
                      padding: const EdgeInsets.only(top: AppSpacing.sm),
                      child: Text(
                        _errorMessage!,
                        style: AppTextStyles.bodySmall.copyWith(
                          color: AppColors.statusError,
                        ),
                      )
                          .animate()
                          .slideX(
                            begin: -0.3,
                            duration: 300.ms,
                          )
                          .fadeIn(),
                    ),
                ],
              ),

              const Spacer(),

              // Continue button
              GlassButton(
                text: 'Continue',
                onPressed: _saveName,
                width: double.infinity,
                height: 56,
                isLoading: _isLoading,
              )
                  .animate()
                  .slideY(
                    begin: 0.3,
                    duration: 500.ms,
                    curve: Curves.easeOut,
                  )
                  .fadeIn(delay: 800.ms),

              const SizedBox(height: AppSpacing.lg),
            ],
          ),
        ),
      ),
    );
  }
}
